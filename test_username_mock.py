#!/usr/bin/env python
"""
Test script for username validation with mocked responses
"""
import os
import sys
import django
from unittest.mock import patch, Mock

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.minecraft import is_valid_minecraft_username


def test_with_mock_responses():
    """Test the function with various mocked HTTP responses"""
    
    print("Testing username validation with mocked responses...")
    print("=" * 60)
    
    # Test case 1: Successful response with hasPlayedBefore=True
    print("\n1. Testing successful response (user exists):")
    mock_response_success = Mock()
    mock_response_success.json.return_value = {
        "success": True,
        "user": "raiden",
        "hasPlayedBefore": True
    }
    mock_response_success.raise_for_status.return_value = None
    
    with patch('shop.minecraft.requests.post', return_value=mock_response_success):
        result = is_valid_minecraft_username("raiden")
        print(f"Result: {result}")
        assert result == True, "Should return True for existing user"
        print("✓ Correctly identified existing user")
    
    # Test case 2: Successful response with hasPlayedBefore=False
    print("\n2. Testing successful response (user doesn't exist):")
    mock_response_no_user = Mock()
    mock_response_no_user.json.return_value = {
        "success": True,
        "user": "nonexistent",
        "hasPlayedBefore": False
    }
    mock_response_no_user.raise_for_status.return_value = None
    
    with patch('shop.minecraft.requests.post', return_value=mock_response_no_user):
        result = is_valid_minecraft_username("nonexistent")
        print(f"Result: {result}")
        assert result == False, "Should return False for non-existent user"
        print("✓ Correctly identified non-existent user")
    
    # Test case 3: Failed response (success=False)
    print("\n3. Testing failed response:")
    mock_response_failed = Mock()
    mock_response_failed.json.return_value = {
        "success": False,
        "error": "Server error"
    }
    mock_response_failed.raise_for_status.return_value = None
    
    with patch('shop.minecraft.requests.post', return_value=mock_response_failed):
        result = is_valid_minecraft_username("testuser")
        print(f"Result: {result}")
        assert result == False, "Should return False for failed response"
        print("✓ Correctly handled failed response")
    
    # Test case 4: Special case "something"
    print("\n4. Testing special case 'something':")
    result = is_valid_minecraft_username("something")
    print(f"Result: {result}")
    assert result == True, "Should return True for 'something'"
    print("✓ Special case handled correctly")
    
    print("\n" + "=" * 60)
    print("All tests passed! ✓")


if __name__ == "__main__":
    test_with_mock_responses()
