#!/usr/bin/env python
"""
Test script for the updated username validation function with dynamic URL building
"""
import os
import sys
import django
from unittest.mock import patch, Mock

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.minecraft import is_valid_minecraft_username
from shop.models import MinecraftServer


def test_dynamic_url_building():
    """Test that the function correctly builds URLs from proxy server configuration"""
    
    print("Testing dynamic URL building for username validation...")
    print("=" * 60)
    
    # Create a test proxy server
    test_server = MinecraftServer.objects.create(
        name="test_proxy",
        display_name="Test Proxy Server",
        ip="*************",
        port=25565,
        query_port=28535,
        proxy=True,
        enabled=True,
        published=True
    )
    
    print(f"Created test proxy server: {test_server.name}")
    print(f"IP: {test_server.ip}, Query Port: {test_server.query_port}")
    
    # Test case 1: Successful response
    print("\n1. Testing with mocked successful response:")
    mock_response = Mock()
    mock_response.json.return_value = {
        "success": True,
        "user": "testuser",
        "hasPlayedBefore": True
    }
    mock_response.raise_for_status.return_value = None
    
    with patch('shop.minecraft.requests.post') as mock_post:
        mock_post.return_value = mock_response
        
        result = is_valid_minecraft_username("testuser")
        
        # Verify the function was called with the correct URL
        expected_url = f"http://{test_server.ip}:{test_server.query_port}/"
        mock_post.assert_called_once()
        
        # Get the actual call arguments
        call_args = mock_post.call_args
        actual_url = call_args[0][0]  # First positional argument
        
        print(f"Expected URL: {expected_url}")
        print(f"Actual URL: {actual_url}")
        print(f"Result: {result}")
        
        assert actual_url == expected_url, f"URL mismatch: expected {expected_url}, got {actual_url}"
        assert result == True, "Should return True for existing user"
        print("✓ URL built correctly and function returned expected result")
    
    # Test case 2: No proxy server configured
    print("\n2. Testing with no proxy server:")
    # Disable the test server
    test_server.enabled = False
    test_server.save()
    
    result = is_valid_minecraft_username("testuser")
    print(f"Result: {result}")
    assert result == False, "Should return False when no proxy server is available"
    print("✓ Correctly handled missing proxy server")
    
    # Test case 3: Proxy server without query port
    print("\n3. Testing proxy server without query port:")
    test_server.enabled = True
    test_server.query_port = None
    test_server.save()
    
    result = is_valid_minecraft_username("testuser")
    print(f"Result: {result}")
    assert result == False, "Should return False when proxy server has no query port"
    print("✓ Correctly handled missing query port")
    
    # Test case 4: Special case "something"
    print("\n4. Testing special case 'something':")
    result = is_valid_minecraft_username("something")
    print(f"Result: {result}")
    assert result == True, "Should return True for 'something'"
    print("✓ Special case handled correctly")
    
    # Clean up
    test_server.delete()
    
    print("\n" + "=" * 60)
    print("All tests passed! ✓")


if __name__ == "__main__":
    test_dynamic_url_building()
