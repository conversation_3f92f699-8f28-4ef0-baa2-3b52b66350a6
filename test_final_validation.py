#!/usr/bin/env python
"""
Test script for the final username validation function with dynamic URL building
"""
import os
import sys
import django
from unittest.mock import patch, Mock

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.minecraft import is_valid_minecraft_username
from shop.models import MinecraftServer


def test_final_validation():
    """Test the final implementation of username validation"""
    
    print("Testing final username validation implementation...")
    print("=" * 60)
    
    # Check current proxy server configuration
    proxy_server = MinecraftServer.objects.filter(proxy=True, enabled=True).first()
    if proxy_server:
        print(f"Using proxy server: {proxy_server.name}")
        print(f"IP: {proxy_server.ip}, Query Port: {proxy_server.query_port}")
        expected_url = f"http://{proxy_server.ip}:{proxy_server.query_port}/"
        print(f"Expected URL: {expected_url}")
    else:
        print("No enabled proxy server found")
        return
    
    # Test case 1: Verify URL building with mocked response
    print("\n1. Testing URL building with mocked response:")
    mock_response = Mock()
    mock_response.json.return_value = {
        "success": True,
        "user": "testuser",
        "hasPlayedBefore": True
    }
    mock_response.raise_for_status.return_value = None
    
    with patch('shop.minecraft.requests.post') as mock_post:
        mock_post.return_value = mock_response
        
        result = is_valid_minecraft_username("testuser")
        
        # Verify the function was called with the correct URL
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        actual_url = call_args[0][0]  # First positional argument
        
        print(f"Actual URL used: {actual_url}")
        print(f"Result: {result}")
        
        assert actual_url == expected_url, f"URL mismatch: expected {expected_url}, got {actual_url}"
        assert result == True, "Should return True for existing user"
        print("✓ URL built correctly from proxy server configuration")
    
    # Test case 2: Test different response scenarios
    print("\n2. Testing user doesn't exist scenario:")
    mock_response_no_user = Mock()
    mock_response_no_user.json.return_value = {
        "success": True,
        "user": "nonexistent",
        "hasPlayedBefore": False
    }
    mock_response_no_user.raise_for_status.return_value = None
    
    with patch('shop.minecraft.requests.post', return_value=mock_response_no_user):
        result = is_valid_minecraft_username("nonexistent")
        print(f"Result: {result}")
        assert result == False, "Should return False for non-existent user"
        print("✓ Correctly handled non-existent user")
    
    # Test case 3: Test server error scenario
    print("\n3. Testing server error scenario:")
    mock_response_error = Mock()
    mock_response_error.json.return_value = {
        "success": False,
        "error": "Server error"
    }
    mock_response_error.raise_for_status.return_value = None
    
    with patch('shop.minecraft.requests.post', return_value=mock_response_error):
        result = is_valid_minecraft_username("erroruser")
        print(f"Result: {result}")
        assert result == False, "Should return False for server error"
        print("✓ Correctly handled server error")
    
    # Test case 4: Special case "something"
    print("\n4. Testing special case 'something':")
    result = is_valid_minecraft_username("something")
    print(f"Result: {result}")
    assert result == True, "Should return True for 'something'"
    print("✓ Special case handled correctly")
    
    # Test case 5: Test with actual request (if server is reachable)
    print("\n5. Testing with actual request to proxy server:")
    try:
        result = is_valid_minecraft_username("raiden")
        print(f"Result for 'raiden': {result}")
        print("✓ Actual HTTP request completed (result may vary based on server state)")
    except Exception as e:
        print(f"Actual request failed (expected if server is unreachable): {e}")
    
    print("\n" + "=" * 60)
    print("All tests completed successfully! ✓")
    print("\nSummary of changes:")
    print("- ✅ Removed hardcoded URL")
    print("- ✅ Added dynamic URL building from proxy server configuration")
    print("- ✅ Added validation for proxy server and query port existence")
    print("- ✅ Maintained all existing functionality and error handling")
    print("- ✅ Function now uses MinecraftServer.objects.filter(proxy=True, enabled=True).first()")


if __name__ == "__main__":
    test_final_validation()
