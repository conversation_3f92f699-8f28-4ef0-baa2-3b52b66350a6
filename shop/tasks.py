from .minecraft import send_command_to_minecraft_server
from .models import Purchase, PurchaseItem, CommandJob, MinecraftServer, PlayerCountSnapshot
from django.utils.timezone import now
from datetime import timedelta
from django_q.tasks import async_task
from mcstatus import JavaServer
import logging

logger = logging.getLogger(__name__)



def revoke_purchase_item(purchase_item_id: int):
    """
    Revoke access for a specific purchase item when it expires.
    This function handles individual items in multi-item purchases.
    """
    try:
        purchase_item = PurchaseItem.objects.get(id=purchase_item_id)
        if (
                purchase_item.subscription_status == Purchase.SubscriptionStatus.SUBSCRIBED
                and purchase_item.expires_at
                and purchase_item.expires_at <= now()
        ):
            # Check if there's a newer active subscription for the same user and item
            has_renewed = PurchaseItem.objects.filter(
                purchase__minecraft_username=purchase_item.purchase.minecraft_username,
                item=purchase_item.item,
                subscription_status=Purchase.SubscriptionStatus.SUBSCRIBED,
                expires_at__gt=now()
            ).exclude(id=purchase_item.id).exists()

            # If renewed, don't revoke access — only expire this record
            if has_renewed:
                purchase_item.subscription_status = Purchase.SubscriptionStatus.EXPIRED
                purchase_item.save()
                return

            # Otherwise, revoke access
            server = purchase_item.item.minecraft_server
            item = purchase_item.item

            # Execute revoke commands if they exist
            if server and item.revoke_commands:
                revoke_commands = [cmd.strip() for cmd in item.revoke_commands.split(',') if cmd.strip()]
                command_success = True

                # Execute revoke commands for each quantity of the item
                for _ in range(purchase_item.quantity):
                    for command in revoke_commands:
                        command = command.replace("{username}", purchase_item.purchase.minecraft_username)
                        if not send_command_to_minecraft_server(server, command):
                            command_success = False
                            purchase_item.subscription_status = Purchase.SubscriptionStatus.FAILED_REVOKE
                            purchase_item.save()
                            break
                    if not command_success:
                        break

                if command_success:
                    purchase_item.subscription_status = Purchase.SubscriptionStatus.EXPIRED
                    purchase_item.save()

    except PurchaseItem.DoesNotExist:
        pass


def timeout_unpaid_purchases():
    """
    Check for purchases created longer than 10 minutes ago with state "created"
    and mark them as timeout.
    """
    timeout_threshold = now() - timedelta(minutes=10)

    # Find purchases that are still in "created" state and older than 10 minutes
    unpaid_purchases = Purchase.objects.filter(
        state=Purchase.State.CREATED,
        created_at__lt=timeout_threshold
    )

    # Update them to timeout state
    timeout_count = unpaid_purchases.update(state=Purchase.State.TIMEOUT)

    if timeout_count > 0:
        print(f"Marked {timeout_count} purchases as timeout")

    return timeout_count


def create_command_jobs_for_purchase(purchase_id: int):
    """
    Create CommandJob instances for all commands in a purchase.
    This should be called after payment verification is successful.
    """
    try:
        purchase = Purchase.objects.get(id=purchase_id)

        for purchase_item in purchase.purchase_items.all():
            item = purchase_item.item
            server = item.minecraft_server

            if server and item.commands:
                commands = [cmd.strip() for cmd in item.commands.split(',') if cmd.strip()]
                sequence_order = 1

                # Create command jobs for each quantity of the item
                for quantity_index in range(purchase_item.quantity):
                    for command in commands:
                        # Replace username placeholder
                        processed_command = command.replace("{username}", purchase.minecraft_username)

                        # Create CommandJob
                        CommandJob.objects.create(
                            purchase_item=purchase_item,
                            command_text=processed_command,
                            sequence_order=sequence_order
                        )
                        sequence_order += 1

        # Start executing commands for this purchase
        async_task('shop.tasks.execute_purchase_commands', purchase_id)
        logger.info(f"Created command jobs and started execution for purchase {purchase_id}")

    except Purchase.DoesNotExist:
        logger.error(f"Purchase {purchase_id} not found when creating command jobs")
    except Exception as e:
        logger.error(f"Error creating command jobs for purchase {purchase_id}: {e}")


def execute_purchase_commands(purchase_id: int):
    """
    Execute all commands for a purchase sequentially.
    This is the main orchestrator task.
    """
    try:
        purchase = Purchase.objects.get(id=purchase_id)
        logger.info(f"Starting command execution for purchase {purchase_id}")

        # Get all purchase items
        for purchase_item in purchase.purchase_items.all():
            # Execute commands for this purchase item sequentially
            execute_purchase_item_commands(purchase_item.id)

        # Update purchase state based on command results
        purchase.update_state_from_commands()
        logger.info(f"Completed command execution for purchase {purchase_id}")

    except Purchase.DoesNotExist:
        logger.error(f"Purchase {purchase_id} not found during command execution")
    except Exception as e:
        logger.error(f"Error executing commands for purchase {purchase_id}: {e}")


def execute_purchase_item_commands(purchase_item_id: int):
    """
    Execute all commands for a specific purchase item sequentially.
    """
    try:
        purchase_item = PurchaseItem.objects.get(id=purchase_item_id)
        command_jobs = purchase_item.command_jobs.filter(state=CommandJob.State.PENDING).order_by('sequence_order')

        logger.info(f"Executing {command_jobs.count()} commands for purchase item {purchase_item_id}")

        for command_job in command_jobs:
            # Execute this command
            success = execute_single_command(command_job.id)

            # If command failed, log it but continue executing remaining commands
            if not success:
                logger.error(f"Command {command_job.id} failed, but continuing with remaining commands for purchase item {purchase_item_id}")

        # Update purchase state after this item's commands are processed
        purchase_item.purchase.update_state_from_commands()

    except PurchaseItem.DoesNotExist:
        logger.error(f"PurchaseItem {purchase_item_id} not found during command execution")
    except Exception as e:
        logger.error(f"Error executing commands for purchase item {purchase_item_id}: {e}")


def execute_single_command(command_job_id: int) -> bool:
    """
    Execute a single command job.
    Returns True if successful, False if failed.
    """
    try:
        command_job = CommandJob.objects.get(id=command_job_id)

        # Mark as running
        command_job.state = CommandJob.State.RUNNING
        command_job.started_at = now()
        command_job.save()

        logger.info(f"Executing command: {command_job.command_text}")

        # Get the server from the purchase item
        server = command_job.purchase_item.item.minecraft_server

        # Execute the command
        success = send_command_to_minecraft_server(server, command_job.command_text)

        # Update command job state
        if success:
            command_job.state = CommandJob.State.SUCCESSFUL
            command_job.completed_at = now()
            command_job.error_message = None
            logger.info(f"Command {command_job_id} executed successfully")
        else:
            command_job.state = CommandJob.State.FAILED
            command_job.completed_at = now()
            command_job.error_message = "Command execution failed"
            logger.error(f"Command {command_job_id} failed to execute")

        command_job.save()
        return success

    except CommandJob.DoesNotExist:
        logger.error(f"CommandJob {command_job_id} not found")
        return False
    except Exception as e:
        logger.error(f"Error executing command job {command_job_id}: {e}")
        # Mark command as failed
        try:
            command_job = CommandJob.objects.get(id=command_job_id)
            command_job.state = CommandJob.State.FAILED
            command_job.completed_at = now()
            command_job.error_message = str(e)
            command_job.save()
        except:
            pass
        return False


def collect_player_counts():
    """
    Collect and save player count snapshots for all enabled Minecraft servers.
    This task is designed to run periodically to track server population over time.
    """
    logger.info("Starting player count collection task")

    # Get all enabled servers
    servers = MinecraftServer.objects.filter(enabled=True)

    if not servers.exists():
        logger.warning("No enabled servers found for player count collection")
        return 0

    successful_queries = 0

    for server in servers:
        try:
            logger.info(f"Querying player count for server: {server.name} ({server.ip}:{server.port})")

            # Create server address
            server_address = f"{server.ip}:{server.port}"

            # Query the server
            minecraft_server = JavaServer.lookup(server_address)
            status = minecraft_server.status()

            # Create successful snapshot
            PlayerCountSnapshot.objects.create(
                server=server,
                online_players=status.players.online,
                max_players=status.players.max,
                query_successful=True
            )

            successful_queries += 1
            logger.info(f"Successfully recorded player count for {server.name}: {status.players.online}/{status.players.max}")

        except Exception as e:
            # Create failed snapshot with error message
            error_msg = str(e)
            logger.error(f"Failed to query server {server.name}: {error_msg}")

            PlayerCountSnapshot.objects.create(
                server=server,
                online_players=0,
                max_players=0,
                query_successful=False,
                error_message=error_msg
            )

    logger.info(f"Player count collection completed. Successfully queried {successful_queries}/{servers.count()} servers")
    return successful_queries
