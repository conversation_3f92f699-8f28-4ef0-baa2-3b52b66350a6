#!/usr/bin/env python
"""
Test script for the updated username validation function
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.minecraft import is_valid_minecraft_username


def test_username_validation():
    """Test the updated username validation function"""
    
    # Test cases
    test_usernames = [
        "raiden",      # Example from the user's request
        "something",   # Special case that should return True
        "nonexistent", # Likely non-existent user
        "testuser123", # Another test case
    ]
    
    print("Testing username validation with HTTP proxy server...")
    print("=" * 60)
    
    for username in test_usernames:
        print(f"\nTesting username: '{username}'")
        try:
            result = is_valid_minecraft_username(username)
            print(f"Result: {result}")
            
            if username == "something":
                if result:
                    print("✓ Special case 'something' correctly returned True")
                else:
                    print("✗ Special case 'something' should return True")
            else:
                print(f"✓ HTTP request completed for '{username}'")
                
        except Exception as e:
            print(f"✗ Error testing '{username}': {e}")
    
    print("\n" + "=" * 60)
    print("Test completed!")


if __name__ == "__main__":
    test_username_validation()
