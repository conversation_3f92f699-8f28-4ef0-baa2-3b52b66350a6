#!/usr/bin/env python
"""
Test the regex pattern
"""
import re

def test_regex():
    pattern = r'^\w{3,16}$'
    test_cases = [
        "abc",  # 3 chars
        "test",  # 4 chars
        "a1234567890123456",  # 17 chars - should fail
        "a123456789012345",   # 16 chars - should pass
        "abcdefghijklmnop",   # 16 chars - should pass
    ]
    
    for test in test_cases:
        result = re.match(pattern, test)
        print(f"'{test}' (length: {len(test)}) -> {bool(result)}")

if __name__ == "__main__":
    test_regex()
