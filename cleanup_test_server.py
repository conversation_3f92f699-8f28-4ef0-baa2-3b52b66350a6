#!/usr/bin/env python
"""
Clean up test proxy server
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import MinecraftServer


def cleanup_test_server():
    """Remove test proxy server if it exists"""
    
    test_servers = MinecraftServer.objects.filter(name="test_proxy")
    if test_servers.exists():
        count = test_servers.count()
        test_servers.delete()
        print(f"Deleted {count} test proxy server(s)")
    else:
        print("No test proxy servers found to delete")


if __name__ == "__main__":
    cleanup_test_server()
