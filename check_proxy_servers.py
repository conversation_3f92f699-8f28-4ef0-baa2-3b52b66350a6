#!/usr/bin/env python
"""
Check existing proxy servers in the database
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import MinecraftServer


def check_proxy_servers():
    """Check what proxy servers exist in the database"""
    
    print("Checking existing proxy servers...")
    print("=" * 60)
    
    # Get all proxy servers
    proxy_servers = MinecraftServer.objects.filter(proxy=True)
    
    print(f"Found {proxy_servers.count()} proxy servers:")
    
    for server in proxy_servers:
        print(f"\nServer: {server.name}")
        print(f"  Display Name: {server.display_name}")
        print(f"  IP: {server.ip}")
        print(f"  Port: {server.port}")
        print(f"  Query Port: {server.query_port}")
        print(f"  Enabled: {server.enabled}")
        print(f"  Published: {server.published}")
        print(f"  Proxy: {server.proxy}")
    
    # Get enabled proxy servers
    enabled_proxy_servers = MinecraftServer.objects.filter(proxy=True, enabled=True)
    print(f"\nEnabled proxy servers: {enabled_proxy_servers.count()}")
    
    if enabled_proxy_servers.exists():
        first_proxy = enabled_proxy_servers.first()
        print(f"First enabled proxy server: {first_proxy.name} ({first_proxy.ip}:{first_proxy.query_port})")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    check_proxy_servers()
